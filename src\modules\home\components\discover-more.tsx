'use client';

import Link from 'next/link';

const DiscoverMore = () => {
  return (
    <section className="relative h-[500px] overflow-hidden">
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url("/images/download.jpeg")`,
        }}
      />

      <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/10" />    

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container mx-auto px-4 w-full">
          <div className="max-w-2xl">
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
              FIXED-WING SUPPORT
            </h2>
            
            <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
              Currently stocking in excess of rotary and fixed wing aircraft parts.
            </p>
            
            <Link 
              href="/aircraft"
              className="inline-block"
            >
              <button className="group relative px-8 py-3 border-2 bg-primary border-white text-white font-semibold text-lg tracking-wider hover:bg-secondary hover:text-white transition-all duration-300 transform hover:scale-105">
                <span className="relative z-10">DISCOVER MORE</span>
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-white transform scale-x-100 group-hover:scale-x-0 transition-transform duration-300" />
              </button>
            </Link>
          </div>
        </div>
      </div>


    </section>
  );
};

export default DiscoverMore;