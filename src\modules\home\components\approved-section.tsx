'use client';

import Image from 'next/image';

const logos = [
  {
    src: '/images/approved/buddha-air.jpg',
    alt: 'Royal Aeronautical Society Certification',
    width: 150,
    height: 150
  },
  {
    src: '/images/approved/mro.png',
    alt: 'FAA Approved Supplier Certification',
    width: 150,
    height: 150
  },
  {
    src: '/images/approved/nepal-army-logo.jpg',
    alt: 'EASA Approved Supplier Certification',
    width: 150,
    height: 150
  }
];

const ApprovedSuppliersSection = () => {
  return (
    <section className="py-8 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8">

          {/* Left Content */}
          <div className="lg:w-1/2">
            <h2 className="text-xl text-start md:text-2xl lg:text-3xl text-gray-700 mb-2">
              Approved Suppliers to National Carriers, Major MROs, Lessors and Government Operators. We are trusted partners with top aviation organizations worldwide.

            </h2>
            <p className="mt-2 text-gray-600">
            </p>
          </div>

          {/* Right Content - Logos */}
          <div className="lg:w-1/2 flex items-center justify-center lg:justify-end flex-wrap gap-15">
            {logos.map((logo, index) => (
              <Image
                key={index}
                src={logo.src}
                alt={logo.alt}
                width={logo.width}
                height={logo.height}
                className="hover:scale-105 transition-transform duration-200"
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ApprovedSuppliersSection;
